import os
import json

# root_path = "Data"
# data = {}
#
# for subdir in os.listdir(root_path):
#     subdir_path = os.path.join(root_path, subdir)
#     if os.path.isdir(subdir_path):
#         for file in os.listdir(subdir_path):
#             file_path = os.path.join(subdir_path, file)
#             if os.path.isfile(file_path):
#                 with open(file_path, 'r', encoding='utf-8') as f:
#                     content = f.read()
#                     data = content
#
# with open("all_data.json", "w", encoding="utf-8") as json_file:
#     json.dump(data, json_file, ensure_ascii=False, indent=4)
#

with open("all_data.json", "r", encoding="utf-8") as json_file:
    data = json.load(json_file)
    print(data)